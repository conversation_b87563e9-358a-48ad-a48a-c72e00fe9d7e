[build]
  publish = ".next"
  command = "npm install --legacy-peer-deps && npm run build"

[build.environment]
  NODE_VERSION = "20"
  # Fase 15: I18n Configuration
  NEXT_PUBLIC_DEFAULT_LOCALE = "es-419"
  NEXT_PUBLIC_SUPPORTED_LOCALES = "es-419,es-MX,es-CO,es-CL,es-PE,es-AR,es-DO,en-US"
  ENABLE_FEATURE_FLAGS = "true"
  ENABLE_MULTI_CURRENCY = "true"
  ENABLE_I18N = "true"
  
  # Fase 14: AI Assistant Environment Variables  
  AI_BACKEND_URL = "http://localhost:3001"
  OPENAI_API_KEY = "sk-dummy-key-for-build"
  ANTHROPIC_API_KEY = "sk-ant-dummy-key-for-build"


[[plugins]]
  package = "@netlify/plugin-nextjs"





[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

# Scheduled Functions - Fase 8: Bill<PERSON> & <PERSON>anzas
[functions.usage-collector]
  schedule = "*/10 * * * *"  # Every 10 minutes

[functions.billing-enforcement]
  schedule = "0 * * * *"  # Every hour

[functions.billing-dunning]
  schedule = "0 */4 * * *"  # Every 4 hours

# Scheduled Functions - Fase 9: Security & Compliance
[functions.schedule-rotate-keys]
  schedule = "0 3 1 */3 *"  # Quarterly key rotation: 1st day at 03:00

[functions.schedule-backup-restore-check]
  schedule = "0 2 * * *"  # Daily backup test at 02:00 UTC

[functions.security-cleanup]
  schedule = "0 4 * * *"  # Daily security cleanup at 04:00 UTC

# Scheduled Functions - Fase 10: Soporte, SLAs & Customer Success
[functions.cs-healthscore]
  schedule = "0 3 * * *"  # Daily health score calculation at 03:00 UTC

[functions.qbr-scheduler]
  schedule = "0 4 * * 1"  # Weekly QBR scheduling on Mondays at 04:00 UTC

[functions.renewal-dunning]
  schedule = "0 5 * * *"  # Daily renewal/dunning check at 05:00 UTC

# Scheduled Functions - Fase 11: Analytics & Reporting
[functions.analytics-collector]
  schedule = "0,15,30,45 * * * *"  # Every 15 minutes - n8n data collection

[functions.analytics-aggregate]
  schedule = "0 2 * * *"  # Daily at 02:00 UTC - rollups and materialized views refresh

[functions.analytics-lag-monitor]
  schedule = "0 * * * *"  # Every hour - data lag monitoring and alerts

[functions.analytics-alerts]
  schedule = "0,30 * * * *"  # Every 30 minutes - anomaly detection and alerts

# Scheduled Functions - Fase 12: Marketplace & Plantillas Monetizadas
[functions.payouts-run]
  schedule = "0 6 15 * *"  # Monthly on 15th at 06:00 UTC - Stripe Connect payouts

[functions.catalog-featured]
  schedule = "0 4 * * *"   # Daily at 04:00 UTC - recalculate featured scores

# Scheduled Functions - Fase 13: Orchestrator Multi-tenancy
[functions.tenants-limits-enforce]
  schedule = "0 * * * *"   # Hourly at :00 - tenant limits enforcement with Stripe sync

[functions.tenants-promote-scan]
  schedule = "0 */4 * * *"  # Every 4 hours at :00 - scan for promotion candidates

# Scheduled Functions - Fase 15: I18n & Multi-Currency
[functions.currency-rates-update]
  schedule = "0 */6 * * *"  # Every 6 hours - update exchange rates

[functions.i18n-export]
  schedule = "0 3 * * 1"    # Weekly on Mondays at 03:00 UTC - export i18n messages

# Scheduled Functions - Fase 16: Legal & Compliance
[functions.sla-credit-calc]
  schedule = "0 6 1 * *"    # Monthly on 1st at 06:00 UTC - calculate SLA credits for previous month

# Fase 4: Security & SRE Functions
[functions.backup-run]
  schedule = "0 2 * * *"  # Daily at 2:00 AM UTC

[functions.restore-sandbox]
  schedule = "0 6 1 * *"  # Monthly on 1st day at 6:00 AM UTC

# Cross-domain redirects for landing integration
[[redirects]]
  from = "/login"
  to = "https://agentevirtualia.com/"
  status = 302
  force = true

[[redirects]]
  from = "/register" 
  to = "https://agentevirtualia.com/"
  status = 302
  force = true

[[redirects]]
  from = "/pricing"
  to = "https://agentevirtualia.com/pricing"
  status = 301
  force = true

[[redirects]]
  from = "/contact"
  to = "https://agentevirtualia.com/contact"
  status = 301
  force = true

# Enhanced Security headers - Fase 4
[[headers]]
  for = "/*"
  [headers.values]
    Strict-Transport-Security = "max-age=63072000; includeSubDomains; preload"
    Content-Security-Policy = "default-src 'self'; img-src 'self' data: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://*.supabase.co wss://*.supabase.co https://*.up.railway.app https://api.netlify.com https://*.agentevirtualia.com; frame-ancestors 'none';"
    X-Content-Type-Options = "nosniff"
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Cross-Origin-Opener-Policy = "same-origin"
    Cross-Origin-Resource-Policy = "cross-site"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

# Cache static assets
[[headers]]
  for = "/_next/static/*"
  [headers.values]
    Cache-Control = "public, immutable, max-age=31536000"